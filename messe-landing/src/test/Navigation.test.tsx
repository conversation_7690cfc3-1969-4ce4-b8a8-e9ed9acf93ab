import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { <PERSON>rowserRouter } from 'react-router-dom'
import App from '../App'

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  )
}

describe('Navigation', () => {
  it('renders all navigation links in header', () => {
    renderWithRouter(<App />)
    
    // Check header navigation links
    const projectsLinks = screen.getAllByText('Projects')
    expect(projectsLinks.length).toBeGreaterThan(0)
    
    const aboutLinks = screen.getAllByText('About Us')
    expect(aboutLinks.length).toBeGreaterThan(0)
    
    // Check phone link
    const phoneLinks = screen.getAllByText('+971 4 548 5887')
    expect(phoneLinks.length).toBeGreaterThan(0)
  })

  it('renders logo links to home', () => {
    renderWithRouter(<App />)
    
    // Check that logo elements exist (they should be links)
    const logoElements = screen.getAllByText('logo')
    expect(logoElements.length).toBeGreaterThanOrEqual(2) // Header and Footer
  })

  it('renders footer navigation links', () => {
    renderWithRouter(<App />)
    
    // Check footer navigation
    const projectsLinks = screen.getAllByText('Projects')
    expect(projectsLinks.length).toBeGreaterThanOrEqual(2) // Header and Footer
    
    const aboutLinks = screen.getAllByText('About Us')
    expect(aboutLinks.length).toBeGreaterThanOrEqual(2) // Header and Footer
    
    // Check Expoglobal group text
    const expoglobalElements = screen.getAllByText('Expoglobal group')
    expect(expoglobalElements.length).toBeGreaterThan(0)
  })

  it('renders CTA buttons with correct links', () => {
    renderWithRouter(<App />)
    
    // Check "All Projects" button
    const allProjectsButtons = screen.getAllByText('All Projects')
    expect(allProjectsButtons.length).toBeGreaterThan(0)
    
    // Check "Get to Know Us Better" button
    const knowUsButtons = screen.getAllByText('Get to Know Us Better')
    expect(knowUsButtons.length).toBeGreaterThan(0)
    
    // Check "Discuss Your Project" button
    const discussButtons = screen.getAllByText('Discuss Your Project')
    expect(discussButtons.length).toBeGreaterThan(0)
  })

  it('has correct href attributes for external links', () => {
    renderWithRouter(<App />)

    // Check phone links have correct href (there are multiple)
    const phoneLinks = screen.getAllByRole('link', { name: /\+971 4 548 5887/i })
    expect(phoneLinks.length).toBeGreaterThan(0)

    // Check that all phone links have correct href
    phoneLinks.forEach(link => {
      expect(link).toHaveAttribute('href', 'tel:+97145485887')
    })
  })
})
